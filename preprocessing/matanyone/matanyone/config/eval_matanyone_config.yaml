defaults:
  - _self_
  - model: base
  - override hydra/job_logging: custom-no-rank.yaml

hydra:
  run:
    dir: ../output/${exp_id}/${dataset}
  output_subdir: ${now:%Y-%m-%d_%H-%M-%S}-hydra

amp: False
weights: pretrained_models/matanyone.pth # default (can be modified from outside)
output_dir: null # defaults to run_dir; specify this to override
flip_aug: False


# maximum shortest side of the input; -1 means no resizing
# With eval_vos.py, we usually just use the dataset's size (resizing done in dataloader)
# this parameter is added for the sole purpose for the GUI in the current codebase
# InferenceCore will downsize the input and restore the output to the original size if needed
# if you are using this code for some other project, you can also utilize this parameter
max_internal_size: -1

# these parameters, when set, override the dataset's default; useful for debugging
save_all: True
use_all_masks: False
use_long_term: False
mem_every: 5

# only relevant when long_term is not enabled
max_mem_frames: 5

# only relevant when long_term is enabled
long_term:
  count_usage: True
  max_mem_frames: 10
  min_mem_frames: 5
  num_prototypes: 128
  max_num_tokens: 10000
  buffer_tokens: 2000

top_k: 30
stagger_updates: 5
chunk_size: -1 # number of objects to process in parallel; -1 means unlimited
save_scores: False
save_aux: False
visualize: False
