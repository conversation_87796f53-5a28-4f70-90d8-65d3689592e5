#!/usr/bin/env python3
"""
Debug script to test audio processing and identify the zero division issue.
"""

import os
import sys
import librosa
import numpy as np
import torch

# Add the project root to the path
sys.path.insert(0, '/home/<USER>/projects/Wan2GP')

def test_audio_processing(audio_path, frames=81, fps=25):
    """Test the audio processing pipeline to identify issues."""
    print(f"Testing audio processing for: {audio_path}")
    
    # Check if file exists
    if not os.path.exists(audio_path):
        print(f"ERROR: Audio file not found: {audio_path}")
        return False
    
    # Get audio duration
    try:
        duration = librosa.get_duration(path=audio_path)
        print(f"Audio duration: {duration:.2f} seconds")
    except Exception as e:
        print(f"ERROR: Failed to get audio duration: {e}")
        return False
    
    # Check if duration is reasonable
    expected_video_duration = frames / fps
    print(f"Expected video duration: {expected_video_duration:.2f} seconds")
    
    if duration < 0.1:
        print(f"ERROR: Audio duration ({duration:.2f}s) is too short")
        return False
    
    # Test the multitalk audio processing
    try:
        from models.wan.multitalk.multitalk import get_full_audio_embeddings
        print("Testing get_full_audio_embeddings...")
        
        # Test with single audio
        full_audio_embs, output_new_audio_data = get_full_audio_embeddings(
            audio_guide1=audio_path,
            audio_guide2=None,
            combination_type="add",
            num_frames=frames,
            fps=fps,
            sr=16000,
            padded_frames_for_embeddings=0,
            min_audio_duration=expected_video_duration
        )
        
        if full_audio_embs is None:
            print("ERROR: get_full_audio_embeddings returned None")
            return False
        
        print(f"Number of audio embeddings: {len(full_audio_embs)}")
        for i, emb in enumerate(full_audio_embs):
            if emb is not None:
                print(f"Audio embedding {i} shape: {emb.shape}")
            else:
                print(f"Audio embedding {i} is None")
        
        # Test window extraction
        from models.wan.multitalk.multitalk import get_window_audio_embeddings
        print("Testing get_window_audio_embeddings...")
        
        audio_proj_split = get_window_audio_embeddings(
            full_audio_embs,
            audio_start_idx=0,
            clip_length=frames
        )
        
        if audio_proj_split is None:
            print("ERROR: get_window_audio_embeddings returned None")
            return False
        
        print(f"Window audio embeddings: {len(audio_proj_split)} parts")
        for i, part in enumerate(audio_proj_split):
            if part is not None:
                print(f"Audio part {i} shape: {part.shape}")
            else:
                print(f"Audio part {i} is None")
        
        # Test the AudioProjModel forward pass
        print("Testing AudioProjModel forward pass...")
        from models.wan.modules.model import AudioProjModel
        
        audio_proj_model = AudioProjModel()
        
        # Check the shapes that would be passed to the model
        audio_embeds = audio_proj_split[0]  # First frame embeddings
        audio_embeds_vf = audio_proj_split[1]  # Latter frame embeddings
        
        print(f"audio_embeds shape: {audio_embeds.shape}")
        print(f"audio_embeds_vf shape: {audio_embeds_vf.shape}")
        
        # Calculate video_length as done in AudioProjModel.forward
        video_length = audio_embeds.shape[1] + audio_embeds_vf.shape[1]
        print(f"Calculated video_length: {video_length}")
        
        if video_length == 0:
            print("ERROR: video_length is 0 - this will cause the division by zero error!")
            return False
        
        # Try the forward pass
        try:
            result = audio_proj_model(audio_embeds, audio_embeds_vf)
            print(f"AudioProjModel forward pass successful, result shape: {result.shape}")
        except Exception as e:
            print(f"ERROR in AudioProjModel forward pass: {e}")
            return False
        
        print("Audio processing test completed successfully!")
        return True
        
    except Exception as e:
        print(f"ERROR in audio processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python test_audio_debug.py <audio_file> [frames] [fps]")
        sys.exit(1)
    
    audio_path = sys.argv[1]
    frames = int(sys.argv[2]) if len(sys.argv) > 2 else 81
    fps = float(sys.argv[3]) if len(sys.argv) > 3 else 25.0
    
    success = test_audio_processing(audio_path, frames, fps)
    
    if success:
        print("\n✓ Audio processing test PASSED")
    else:
        print("\n✗ Audio processing test FAILED")
        sys.exit(1)

if __name__ == "__main__":
    main()
